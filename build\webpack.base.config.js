const webpack = require('webpack');

const path = require('path');
const VueLoaderPlugin = require('vue-loader/lib/plugin');

const FRAME_NAME = '/mtex/';
const PROJECT_NAME = 'generaldisplayboard'; //templ项目代号如 rams, scse
const PUBLIC_PATH = `static_dist/${PROJECT_NAME}Plugin/`;

const baseConfig = {
    //页面入口文件配置
    entry: {
        plugin: './src/script/enter/plugin.js' // 属性名由 plugin 被自动修改为 ${PROJECT_NAME}Plugin
    },
    //入口文件输出配置
    output: {
        // 或根路径开始，如：/mtex/static_dist/${PROJECT_NAME}Plugin/
        publicPath: FRAME_NAME + PUBLIC_PATH, //区别
        filename: 'script/[name].min.js',
        chunkFilename: 'script/[name].[chunkhash:8].min.js'
    },
    plugins: [
        new VueLoaderPlugin(),
        new webpack.ProvidePlugin({
            'window.Quill': 'quill/dist/quill.js',
            Quill: 'quill/dist/quill.js'
        })
    ],
    module: {
        //加载器配置
        rules: [
            { test: /.vue$/, use: 'vue-loader' },
            { test: /.(html)$/, use: { loader: 'html-loader' } },
            {
                test: /.css$/,
                use: [{ loader: 'style-loader' }, { loader: 'css-loader' }]
            },
            {
                test: /.(sass|scss)$/,
                use: ['vue-style-loader', 'css-loader', 'sass-loader']
            },
            {
                test: /.styl(us)?$/,
                use: ['vue-style-loader', 'css-loader', 'stylus-loader']
            },
            {
                test: /\.less$/,
                use: [
                    'vue-style-loader',
                    'css-loader',
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true
                            },
                            additionalData: `@import "${path.resolve(__dirname, '../src/style/global.less')}";`
                        }
                    }
                ]
            },
            {
                test: /.(png|jpg|gif|eot|svg|ttf|woff)$/,
                exclude: [path.resolve(__dirname, './src/script/icons')],
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 1,
                            fallback: 'file-loader',
                            name: '[name].[hash:7].[ext]',
                            outputPath: 'images/'
                        }
                    }
                ]
            },
            {
                test: /\.svg$/,
                loader: 'svg-sprite-loader',
                include: [path.resolve(__dirname, './src/script/icons')],
                options: {
                    symbolId: 'svgIcon-[name]'
                }
            }
        ]
    },
    resolve: {
        //模块别名定义
        alias: {
            vue: 'vue/dist/vue.js',
            '@': path.resolve(__dirname, '../src')
        }
    }
};
baseConfig.entry[PROJECT_NAME + 'Plugin'] = baseConfig.entry.plugin;
delete baseConfig.entry.plugin;
module.exports = {
    baseConfig,
    CONST_INFO: {
        FRAME_NAME,
        PROJECT_NAME,
        PUBLIC_PATH
    }
};
