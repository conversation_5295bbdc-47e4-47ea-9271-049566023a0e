<template>
    <div class="content-container">
        <mtv-gis :ref="gisId" :totaloptions="gisOptions" @onLoad="gisOnLoad"></mtv-gis>
    </div>
</template>

<script>
export default {
    data() {
        return {
            gisId: 'gisId',
            gisOptions: {
                city: null,
                initialZoom: 17,
                options: {
                    antialias: true,
                    mapLayer: {
                        visible: true,
                        type: 22,
                        mapType: 'tx',
                        colorControl: false
                    },
                    cameraControl: {
                        type: '3D',
                        typeButton: true,
                        wheelLevelChange: true,
                        wheelState: true,
                        moveState: true,
                        rotateState: true,
                        minZoom: 4,
                        maxZoom: 17
                    },
                    colorLegend: {
                        visible: false,
                        autoUpdateLegend: true,
                        defaultOption: 'RSRP均值'
                    },
                    layerControl: {
                        visible: false
                    },
                    tool: {
                        visible: false
                    },

                    showInfo: {
                        visible: true,
                        float: true
                    }
                }
            },
            dateActive: 0,
            dates: ['02.18', '02.19', '02.20', '02.21', '02.22', '02.23', '02.24'],
            times: ['0', '1', '2', '3', '4', '5', '6'],
            timeActive: 0
        };
    },
    methods: {
        gisOnLoad() {},
        chooseDate(date) {
            this.dateActive = date;
        },
        chooseTime(time) {
            this.timeActive = time;
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 100%;
    .mtv-gis {
        width: 100%;
        height: 100%;
        margin: 1.11rem 0;
        border-radius: 0.22rem;
        overflow: hidden;
    }
}
</style>
