<template>
    <div class="card-list">
        <div class="card-item" v-for="(item, index) in cardList" :key="index">
            <div class="card-content">
                <img class="map-thumb" :src="cardDefaultImg" />
                <div class="card-info">
                    <div class="card-title">
                        <h3>{{ item.cardName }}</h3>
                        <el-button
                            class="view-btn"
                            type="primary"
                            size="small"
                            @click="handleCardClick(item)"
                            >查看</el-button
                        >
                    </div>
                    <div class="line">
                        <div class="line-one"></div>
                        <div class="line-two"></div>
                    </div>
                    <div class="des">{{ item.cardComment }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import cardDefaultImg from '../../../../../img/homePage/card-default.png';
export default {
    name: 'ContentPanel',
    props: {
        cardList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            cardDefaultImg
        };
    },
    methods: {
        handleCardClick(item) {
            this.$emit('card-click', item);
        }
    }
};
</script>

<style lang="less" scoped>
.card-list {
    display: flex;
    flex-wrap: wrap;
    gap: 1.11rem;
    padding: 0 0.56rem;

    .card-item {
        width: calc(25% - 1rem); // 一行4个，考虑间距
        background: url(../../../../../img/homePage/card-bg.png);
        background-size: 100% 100%;
        border-radius: 0.61rem;
        padding: 1rem 1.11rem 1.78rem;
        box-shadow: 0 0.11rem 0.67rem 0 rgba(0, 0, 0, 0.1);
        cursor: pointer;
        // transition: all 0.3s;

        // &:hover {
        //     transform: translateY(-0.28rem);
        //     box-shadow: 0 0.22rem 1.11rem 0 rgba(0, 0, 0, 0.15);
        // }

        .card-content {
            .map-thumb {
                width: 100%;
                height: 8.89rem;
                background: @background-color;
                border-radius: 0.22rem;
                margin-bottom: 0.83rem;
            }

            .card-info {
                .card-title {
                    color: #92b9e5;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .view-btn {
                        width: 4.11rem;
                        height: 33px;
                    }
                }
                .line {
                    display: flex;
                    height: 0.17rem;
                    margin-bottom: 1.11rem;
                    margin-top: 0.56rem;
                    .line-one {
                        width: 2rem;
                        background: #4986ff;
                    }
                    .line-two {
                        width: 0.94rem;
                        background: #ffaeae;
                    }
                }
                h3 {
                    margin: 0;
                    font-size: 1.11rem;
                    color: #92b9e5;
                    font-weight: bold;
                    font-family:
                        Source Han Sans CN,
                        Source Han Sans CN;
                }
                hr {
                    width: 2.78rem;
                    height: 0.17rem;
                }

                .des {
                    color: @text-color-secondary;
                    font-size: 0.83rem;
                    line-height: 1.5;
                    margin: 0;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 4; // 显示4行
                    overflow: hidden;
                    font-family:
                        Source Han Sans CN,
                        Source Han Sans CN;
                }
            }
        }
    }
}
</style>
