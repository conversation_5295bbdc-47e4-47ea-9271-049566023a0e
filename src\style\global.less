// 全局变量
@primary-color: #135ef4;
@secondary-color: #4986ff;

@text-color: #c3dff6;
@text-color-secondary: #92b9e5;

@border-color: #dcdfe6;
@background-color: #f5f7fa;

// 导入 Element UI 组件样式重写
@import './element-ui/index.less';

body {
    background: red !important;
}

.gdb-date-picker-dark {
    color: #fff !important;
    background: #002a5c !important;
    border: 1px solid #ebeef522 !important;
    .el-button {
        &:not(.el-button--text) {
            background-color: #00326bcc;
            border: 1px solid #0095ff99;
            color: #fff;
        }
        &--text {
            display: none !important;
        }
        &.is-disabled.is-plain {
            background-color: transparent;
        }
        &:hover {
            background-color: #00326b33 !important;
        }
    }
    .el-input__inner {
        background-color: transparent !important;
        border: 1px solid #128bcf99 !important;
        color: #fff !important;
        &::placeholder {
            color: rgba(255, 255, 255, 0.45);
        }
        .el-range-separator {
            color: #fff !important;
        }
    }
    .el-picker-panel {
        color: #fff;
        &__shortcut {
            color: #fff;
            &:hover {
                color: #00ceff;
            }
        }
        &__icon-btn {
            color: #fff;
        }
        &__sidebar {
            background-color: transparent;
            border-right: 1px solid #409bff4d;
        }
        &__footer {
            background-color: transparent;
            border-top: 1px solid #409bff4d;
        }
    }
    .el-date-range-picker {
        &__time-header {
            border-bottom: 1px solid #409bff4d;
        }
        &__content.is-left {
            border-right: 1px solid #409bff4d;
        }
    }
    .el-date-table,
    .el-month-table {
        th {
            border-bottom: 1px solid #409bff4d;
        }
        td {
            &.in-range div {
                background: #093e79;
                &:hover {
                    background: #409bff4d;
                }
            }
            &.disabled div {
                background-color: #093e79;
                opacity: 0.5;
                .cell {
                    background-color: inherit !important;
                }
            }
        }
    }
    .el-time-panel {
        background: #002a5c !important;
        border: 1px solid #ebeef522 !important;
        &[x-placement^='top'] .popper__arrow {
            border-top-color: #ebeef522 !important;
            &::after {
                border-top-color: #002a5c !important;
            }
        }
        &[x-placement^='bottom'] .popper__arrow {
            border-bottom-color: #ebeef522 !important;
            &::after {
                border-bottom-color: #002a5c !important;
            }
        }
        &[x-placement^='left'] .popper__arrow {
            border-left-color: #ebeef522 !important;
            &::after {
                border-left-color: #002a5c !important;
            }
        }
        &[x-placement^='right'] .popper__arrow {
            border-right-color: #ebeef522 !important;
            &::after {
                border-right-color: #002a5c !important;
            }
        }
        &__content {
            &::before {
                border-top: 1px solid #128bcf99;
                border-bottom: 1px solid #128bcf99;
            }
            &::after {
                border-top: 1px solid #128bcf99;
                border-bottom: 1px solid #128bcf99;
            }
        }
        &__footer {
            border-top: 1px solid #128bcf99;
        }
        &__btn {
            color: #fff;
        }
        .el-time-spinner__item {
            color: #fff;
            &.active {
                color: #00ceff;
            }
            &:hover {
                background-color: #00326b33 !important;
            }
        }
        .el-scrollbar__wrap {
            overflow-x: hidden; //将横向滚动条隐藏
        }
    }
    .el-date-picker__header-label {
        color: #fff;
    }
    .el-year-table td .cell {
        color: #fff;
    }
    .el-month-table td .cell {
        color: #fff;
    }
    .el-date-table th {
        color: #999;
        text-align: center;
    }
    .time-select-item {
        &.selected:not(.disabled) {
            color: #00ceff !important;
        }
        &:hover {
            background: #409bff4d !important;
            font-weight: normal !important;
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
.gdb-popover-dark {
    background: #002a5c !important;
    border: 1px solid rgba(64, 155, 255, 0.3) !important;
    &[x-placement^='top'] .popper__arrow {
        border-top-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-top-color: #002a5c !important;
        }
    }
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-bottom-color: #002a5c !important;
        }
    }
    &[x-placement^='left'] .popper__arrow {
        border-left-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-left-color: #002a5c !important;
        }
    }
    &[x-placement^='right'] .popper__arrow {
        border-right-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-right-color: #002a5c !important;
        }
    }
    // 暗色时滚动条调成透明
    .el-scrollbar__wrap {
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        &::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px transparent;
            border-radius: 10px;
            background: transparent;
        }
    }
}
.gdb-tooltip-dark {
    background: #002a5c !important;
    border: 1px solid rgba(64, 155, 255, 0.3) !important;
    &[x-placement^='top'] .popper__arrow {
        border-top-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-top-color: #002a5c !important;
        }
    }
    &[x-placement^='bottom'] .popper__arrow {
        border-bottom-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-bottom-color: #002a5c !important;
        }
    }
    &[x-placement^='left'] .popper__arrow {
        border-left-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-left-color: #002a5c !important;
        }
    }
    &[x-placement^='right'] .popper__arrow {
        border-right-color: rgba(64, 155, 255, 0.3) !important;
        &::after {
            border-right-color: #002a5c !important;
        }
    }
    &.popper__arrow {
        display: none !important;
    }
}
.gdb-select-dropdown-dark {
    .el-select-dropdown__item {
        color: #fff !important;
        &:last-child {
            margin-bottom: 10px;
        }
        &.selected:not(.is-disabled) {
            color: #0095ff !important;
        }
        &.hover {
            background-color: #093e79 !important;
        }
        &:hover {
            background-color: #093e79 !important;
        }
    }
}
.gdb-section-title {
    font-size: 14px;
    font-weight: bold;
    color: #0095ff;
    margin: 16px 0;
    position: relative;
    padding-left: 16px;

    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        background-color: #0095ff;
        border-radius: 50%;
        width: 8px;
        height: 8px;
    }
}
